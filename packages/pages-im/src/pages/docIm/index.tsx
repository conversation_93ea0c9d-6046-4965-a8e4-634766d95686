import {Provider, useAtomValue} from 'jotai';
import {View} from '@tarojs/components';
import {Portal} from '@baidu/vita-ui-cards-common';
import {memo, type FC, useEffect, useState, useCallback} from 'react';
import cx from 'classnames';

import CTopBar from '../../components/CTopBar';
import CMaskLoading from '../../components/CMaskLoading';
import CAntipassPopup from '../../components/CAntipassPopup';
import CPageContainer from '../../components/CPageContainer';
import {createErrorBoundary} from '../../components/ErrorBoundary/ErrorBoundaryHoc';

import {useGetUserData} from '../../hooks/docIm/pageDataController';
import {useInitDataController} from '../../hooks/docIm/useInitDataController';
import {useHandleUserLoginBizAction} from '../../hooks/docIm/useHandleUserBizAction';
import {useGenGlobalModalWithAtom} from '../../hooks/modal/useGenGlobalModalWithAtom';
import {isPaidMed} from '../../utils';

import {resetModalAtom} from '../../store/viewRenderAtom';
import {
    docImAtomStore,
    resetSessionIdAtom,
    getDataForUbcAtom,
    titleDataAtom
} from '../../store/docImAtom';

import {getSystemInfo} from '../../utils/taro';
import {ubcCommonViewSend} from '../../utils/generalFunction/ubc';

import LeavePopUp from './components/LeavePopUp';
import OperatingArea from './components/OperatingArea';
import SessionContent from './components/SessionContent';

import styles from './index.module.less';

const systemInfo = getSystemInfo();
const {barHeight = 0, bigFontSizeClass} = systemInfo;
const contentHeight = process.env.TARO_ENV === 'h5' ? `${window.innerHeight}px` : '100vh';

export const LEAVE_DATA_TRIAGE = {
    leaveInfo: {
        title: '温馨提示',
        content: '离开当前页面，您所填写的信息可能会全部丢失，是否留下继续咨询？',
        btns: [
            {
                text: '放弃咨询',
                type: 'confirm'
            },
            {
                text: '继续咨询',
                type: 'cancel'
            }
        ]
    }
};

/**
 * TriageStreamPage 组件，用于渲染医生 AI 分身页面。
 *
 * @returns 返回渲染后的 React 元素。
 */
const DocImPage: FC = () => {
    const [naviFunc, setNaviFunc] = useState({});
    const [backState, setBackState] = useState('');
    const [showDialog, setShowDialog] = useState(false);

    const isPaidMedScene = isPaidMed();

    const {userData} = useGetUserData();
    const {handleLoginBizAction} = useHandleUserLoginBizAction();
    const {antiPassData, isLoadingFirstHistory, getData, releaseFirstConversation} =
        useInitDataController();

    const titleData = useAtomValue(titleDataAtom);
    const {title = ''} = titleData || {};
    const {modalComponent} = useGenGlobalModalWithAtom(docImAtomStore);

    useEffect(() => {
        getData();

        ubcCommonViewSend({
            value: 'page',
            ext: {
                product_info: {
                    ...getDataForUbcAtom()
                }
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 返回时处理弹窗逻辑
    const onBackDetain = (eventName, navigateBack, navigateHome) => {
        setShowDialog(true);
        setBackState(eventName);
        setNaviFunc({navigateBack, navigateHome});
    };

    // 关闭弹窗按钮
    const closeDialog = useCallback(
        isStay => {
            setShowDialog(false);

            if (isStay) {
                return;
            }

            // 继续返回
            if (typeof naviFunc[backState] === 'function') {
                const timeid = setTimeout(() => {
                    naviFunc[backState]();
                    clearTimeout(timeid);
                }, 300);
            }
        },
        [naviFunc, backState]
    );

    const h5BgColor = `left top / 100% auto no-repeat
        url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiContainerBg${isPaidMedScene ? 'CDMClone' : ''}.png')`;

    const barBg = process.env.TARO_ENV === 'h5' ? h5BgColor : 'inherit';

    const renderTitleLeft = () => {
        // paidMed场景用户进入页面即为在线，无需判断真实医生状态，也无其他状态
        return isPaidMedScene ? (
            <View className={cx(styles.titleLeftWrap, 'wz-flex')}>
                {title}
                <View className={cx(styles.dot, 'wz-ml-30')} />
                <View className={cx(styles.status, 'wz-fs-42 wz-ml-12')}>在线</View>
            </View>
        ) : (
            title
        );
    };

    return (
        <>
            <CPageContainer
                skeletonName='triageIm'
                disableTopBar={true}
                className={cx(
                    styles.docImPageContainer,
                    isPaidMedScene ? styles.containerPaidMed : ''
                )}
            >
                <Portal.provider>
                    <CTopBar
                        title=' '
                        className='white'
                        textColor='#000311'
                        barBg={barBg}
                        blank={true}
                        hideHome={true}
                        backDetain
                        onBackDetain={onBackDetain}
                        titleLeftSlot={renderTitleLeft()}
                        isLogin={userData?.isLogin}
                        menu={
                            !isPaidMedScene
                                ? [
                                    {
                                        icon: 'myOrder',
                                        type: 'navigate',
                                        isNeedLogin: true,
                                        url: '/wenzhen/pages/order/list/index',
                                        logValue: 'top_tips_order_guide_orderList_clk',
                                        onLoginCallback: handleLoginBizAction
                                    }
                                ]
                                : []
                        }
                    />
                    <View
                        className={styles.triageStreamContainer}
                        style={{height: `calc(${contentHeight} - ${barHeight}px)`}}
                    >
                        <SessionContent />
                        <OperatingArea />
                        {process.env.TARO_ENV === 'h5' && antiPassData && (
                            <CAntipassPopup
                                data={antiPassData?.data}
                                fkParams={antiPassData?.fkParams}
                                callback={releaseFirstConversation}
                                refreshParams={antiPassData?.refreshParams}
                            />
                        )}
                        <Portal.slot />
                        {modalComponent}
                    </View>
                </Portal.provider>
            </CPageContainer>
            {/* 挽留弹窗 */}
            <View className={bigFontSizeClass}>
                <LeavePopUp
                    showLeave={showDialog}
                    leaveData={LEAVE_DATA_TRIAGE}
                    closeDialog={closeDialog}
                />
            </View>
            {isLoadingFirstHistory && <CMaskLoading />}
        </>
    );
};

/**
 *
 * @description 用于包裹页面组件，控制渲染时序，明确局部 Provider
 * @returns 返回渲染后的 React 元素。
 */
const DocImPageContainer = () => {
    useEffect(() => {
        return () => {
            resetSessionIdAtom();
            resetModalAtom(docImAtomStore);
            // eslint-disable-next-line no-console
            console.warn('DocImPageContainer 相关 Atom 已重置');
        };
    }, []);

    return (
        <Provider store={docImAtomStore}>
            <DocImPage />
        </Provider>
    );
};

export default createErrorBoundary(memo(DocImPageContainer));
