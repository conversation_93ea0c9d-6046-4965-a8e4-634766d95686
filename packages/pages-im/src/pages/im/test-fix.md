# IM 页面无限更新问题修复

## 问题描述
通过 `Taro.reLaunch` 跳转到 IM 页面后，组件出现无限更新的问题。

## 根本原因分析

### 1. 错误的 Atom 使用方式
- **问题代码**：`const {title = '', menu, titleTips = ''} = getTitleDataAtom();`
- **问题**：`getTitleDataAtom()` 是直接从 store 获取值的函数，不是 React Hook
- **后果**：每次组件重新渲染都会执行这个函数，但不会建立响应式订阅

### 2. 状态重置触发的连锁反应
- `resetSessionIdAtom()` 将 `curSessionIdAtom` 设置为 `undefined`
- `resetModalAtom()` 重置 modal 相关状态
- 这些状态变化触发依赖组件重新渲染
- `useInitDataController` 中的 `useEffect` 监听 `curSessionId` 变化，可能触发数据获取

### 3. 无限循环的形成
1. `Taro.reLaunch` 跳转 → 组件卸载时执行清理函数 → 重置 atoms
2. 新页面加载 → `useInitDataController` 检测到状态变化 → 触发数据获取
3. 数据获取过程中再次触发状态更新 → 组件重新渲染
4. 错误的 atom 使用方式导致每次渲染都可能触发新的更新
5. 循环往复

## 修复方案

### 1. 修复 Atom 使用方式 ✅

#### 修复的文件：
- `packages/pages-im/src/pages/im/index.tsx` (第50行) - `getTitleDataAtom()` → `useAtomValue(titleDataAtom)`
- `packages/pages-im/src/pages/docIm/index.tsx` (第74行) - `getTitleDataAtom()` → `useAtomValue(titleDataAtom)`
- `packages/pages-im/src/components/HistoryRecordPopup/index.tsx` (第81行) - `getTitleDataAtom()` → `useAtomValue(titleDataAtom)`
- `packages/pages-im/src/hooks/triageStream/useConversationDataController/index.ts` (第833行, 第908行) - `getUserInterruptedAtom()`, `getUserDataAtom()` → `useAtomValue(isUserInterruptedAtom)`, `useAtomValue(userDataAtom)`
- `packages/pages-im/src/store/docImAtom/index.ts` (第137行) - 修复 `getUserInterruptedAtom` 函数缺少返回值

```typescript
// 修复前（错误）
const {title = '', menu, titleTips = ''} = getTitleDataAtom();

// 修复后（正确）
const titleData = useAtomValue(titleDataAtom);
const {title = '', menu, titleTips = ''} = titleData || {};
```

### 2. 优化清理函数的执行时机 ✅
```typescript
// 修复前
useEffect(() => {
    return () => {
        resetSessionIdAtom();
        resetModalAtom(triageStreamAtomStore);
        console.warn('TriageStreamPageContainer 相关 Atom 已重置');
    };
}, []);

// 修复后
useEffect(() => {
    const timeoutId = setTimeout(() => {
        // 页面已经稳定，可以安全地设置清理函数
    }, 100);

    return () => {
        clearTimeout(timeoutId);
        // 延迟执行清理，避免与页面初始化逻辑冲突
        setTimeout(() => {
            resetSessionIdAtom();
            resetModalAtom(triageStreamAtomStore);
            console.warn('TriageStreamPageContainer 相关 Atom 已重置');
        }, 0);
    };
}, []);
```

### 3. 导入修复 ✅
确保所有修复的文件都正确导入了 `useAtomValue` 和 `titleDataAtom`：

```typescript
// 添加导入
import {useAtomValue} from 'jotai';
import {titleDataAtom} from '../../store/triageStreamAtom'; // 或相应路径
```

## 测试验证

### 自动化测试
使用提供的测试脚本 `test-navigation.js`：

```javascript
// 在浏览器控制台中运行
window.testIMNavigation.testReLaunchToIM(); // 测试跳转
window.testIMNavigation.checkPageStability(); // 检查稳定性
window.testIMNavigation.checkFixStatus(); // 检查修复状态
```

### 手动测试步骤
1. 在其他页面通过 `Taro.reLaunch` 跳转到 IM 页面
2. 观察控制台是否有无限的重新渲染日志
3. 检查页面是否正常加载和运行
4. 验证状态重置是否正常工作
5. 检查是否有 React Hook 相关的错误

### 预期结果
- ✅ 页面正常加载，无无限更新
- ✅ 状态重置正常执行（最多1次）
- ✅ 组件渲染稳定
- ✅ 无 React Hook 使用错误
- ✅ 无控制台错误

### 问题排查
如果仍有问题，检查：
1. 是否还有其他地方直接调用 `getTitleDataAtom()`
2. 是否有其他类似的 `getXxxAtom()` 函数被错误使用
3. 是否有其他导致无限循环的状态依赖

## 注意事项
1. 确保所有 atom 的使用都通过正确的 React Hook
2. 避免在组件中直接调用 store 的 get 方法
3. 清理函数的执行时机要合理，避免与初始化逻辑冲突
4. 定期检查新增代码是否遵循正确的 atom 使用模式
