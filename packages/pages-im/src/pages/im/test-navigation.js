// 测试 Taro.reLaunch 跳转到 IM 页面的脚本
// 在浏览器控制台中运行此脚本来测试修复效果

console.log('开始测试 IM 页面跳转修复...');

// 模拟从其他页面跳转到 IM 页面
function testReLaunchToIM() {
    console.log('执行 Taro.reLaunch 跳转到 IM 页面...');
    
    // 记录跳转前的状态
    const beforeJump = {
        timestamp: Date.now(),
        url: window.location.href
    };
    
    console.log('跳转前状态:', beforeJump);
    
    // 执行跳转（在实际环境中使用）
    if (typeof Taro !== 'undefined' && Taro.reLaunch) {
        Taro.reLaunch({
            url: '/pages/im/index',
            success: () => {
                console.log('跳转成功');
                
                // 延迟检查页面状态
                setTimeout(() => {
                    checkPageStability();
                }, 2000);
            },
            fail: (err) => {
                console.error('跳转失败:', err);
            }
        });
    } else {
        console.log('Taro 环境不可用，使用模拟跳转');
        // 在开发环境中模拟跳转
        window.location.href = '/pages/im/index';
    }
}

// 检查页面稳定性
function checkPageStability() {
    console.log('检查页面稳定性...');
    
    let renderCount = 0;
    const maxRenderCount = 10; // 最大允许的渲染次数
    
    // 监听组件渲染
    const originalConsoleWarn = console.warn;
    console.warn = function(...args) {
        if (args[0] && args[0].includes('TriageStreamPageContainer 相关 Atom 已重置')) {
            renderCount++;
            console.log(`检测到组件重置，当前次数: ${renderCount}`);
            
            if (renderCount > maxRenderCount) {
                console.error('❌ 检测到无限更新！组件重置次数超过阈值');
                console.warn = originalConsoleWarn; // 恢复原始 console.warn
                return;
            }
        }
        originalConsoleWarn.apply(console, args);
    };
    
    // 5秒后检查结果
    setTimeout(() => {
        console.warn = originalConsoleWarn; // 恢复原始 console.warn
        
        if (renderCount <= 1) {
            console.log('✅ 页面稳定，无无限更新问题');
        } else if (renderCount <= maxRenderCount) {
            console.log(`⚠️ 页面基本稳定，但有 ${renderCount} 次重置`);
        } else {
            console.error('❌ 页面不稳定，可能存在无限更新');
        }
        
        console.log('测试完成');
    }, 5000);
}

// 检查修复状态
function checkFixStatus() {
    console.log('检查修复状态...');
    
    // 检查是否正确使用了 useAtomValue
    const pageElement = document.querySelector('[data-testid="triage-stream-page"]');
    if (pageElement) {
        console.log('✅ 页面元素存在');
    } else {
        console.log('⚠️ 页面元素未找到');
    }
    
    // 检查控制台是否有错误
    const errors = [];
    const originalConsoleError = console.error;
    console.error = function(...args) {
        errors.push(args);
        originalConsoleError.apply(console, args);
    };
    
    setTimeout(() => {
        console.error = originalConsoleError;
        if (errors.length === 0) {
            console.log('✅ 无控制台错误');
        } else {
            console.log('⚠️ 发现控制台错误:', errors);
        }
    }, 3000);
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testReLaunchToIM,
        checkPageStability,
        checkFixStatus
    };
} else {
    // 在浏览器环境中直接可用
    window.testIMNavigation = {
        testReLaunchToIM,
        checkPageStability,
        checkFixStatus
    };
    
    console.log('测试函数已加载到 window.testIMNavigation');
    console.log('使用方法:');
    console.log('1. window.testIMNavigation.testReLaunchToIM() - 测试跳转');
    console.log('2. window.testIMNavigation.checkPageStability() - 检查稳定性');
    console.log('3. window.testIMNavigation.checkFixStatus() - 检查修复状态');
}
