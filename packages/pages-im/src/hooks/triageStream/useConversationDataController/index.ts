import {useCallback, useRef} from 'react';
import {genImMsgKey} from '@baidu/vita-utils-shared';
import {useAtom, useAtomValue, useSetAtom} from 'jotai';

import {
    updateCurSessionMsgIdsAtom,
    updateTriageStreamMsgAtom,
    endTriageStreamMsgAtom,
    createTriageStreamMsgAtom,
    createResendMsgArgAtom,
    getTriageStreamMsgDataByKey,
    getMsgIdToKeyMappingAtomVal,
    setMsgIdToKeyMapping,
    updateMsgRiskControlAtom
} from '../../../store/triageStreamAtom/msg';
import {
    lastMsgIdAtom,
    curSessionIdAtom,
    updateData<PERSON>orUbc<PERSON>tom,
    lastConversionMsgIdAtom,
    userDataAtom,
    isUserInterruptedAtom,
    setIsGeneratingAtom
} from '../../../store/triageStreamAtom';
import {
    getAgreementInfo<PERSON>tom,
    resetAgreementInfo<PERSON>tom
} from '../../../store/triageStreamAtom/otherData';

import {useGetUrlParams} from '../../../hooks/common';
import {useScrollControl} from '../../common/useScrollControl';

import {
    ONLINE_HOST as APP_ONLINE_HOST,
    BUCKET_NAME as APP_BUCKET_NAME
} from '../../../constants/common';
import {
    MSG_CARDID_ENUM,
    MSG_CARDID_ENUM_STREAM,
    MSG_THINKING_MSG_KEY_PREFIX,
    MSG_CARDID_ENUM_STREAM_TYPE,
    type MSG_CARDID_TYPE
} from '../../../constants/msg';

import {showToast} from '../../../utils/customShowToast';
import {uploadFileToBos} from '../../../utils/basicAbility/upload';
import {allSettled} from '../../../utils/generalFunction/allSettled';
import {ubcCommonViewSend} from '../../../utils/generalFunction/ubc';

import {
    conversationSSE,
    type SSEProcessorInstance
} from '../../../models/services/triageStream/sse';
import {API_HOST as APP_API_HOST} from '../../../models/apis/host';
import {getUseractionReq} from '../../../models/services/triageStream';

import type {MsgId} from '../../../typings';
import type {IPicProps} from '../../../typings/upload';
import type {CreateConversationArgs, RichTextContent} from '../index.d';
import type {InputImgMap} from '../../../models/services/triageStream/sse/index.d';
import type {AgreementDataType} from '../../../models/services/triageStream/index.d';
import type {MsgItemType, SSEResponseType} from '../../../store/triageStreamAtom/index.type.ts';

import {
    convertSystemMsg,
    convertStreamMsgProtocol,
    updateSpecialCardAdjectiveMsgId
} from '../msgUtils';
import {useSessionUtils} from '../useSessionUtils';
import {useGetInputData} from '../pageDataController';
import {useHandleUserLoginBizAction} from '../useHandleUserBizAction';
import {useGetSwanMsgListSceneStatus} from '../useGetSwanMsgListSceneStatus';
import {useGetSessionMsgIds, useMsgDataGetController} from '../dataController';

import {
    transformMsg,
    sendSwanMsg,
    failedMsgPrefix,
    syncSwanMsgStatus,
    dispatchSwanimAction
} from './swanMsgCenter';

const bucketConfName =
    APP_API_HOST && APP_ONLINE_HOST.indexOf(APP_API_HOST) > -1
        ? APP_BUCKET_NAME[2]
        : `${APP_BUCKET_NAME[2]}-test`;

/**
 * SSE实例管理队列 - 全局状态
 */
const curSSEInstance: {
    id: symbol;
    thinkingMsgKey: MsgId;
    relatedMsgIds: MsgId[];
    instance: SSEProcessorInstance<SSEResponseType>;
}[] = [];

/**
 * SSE主动中断状态记录
 */
let isActivelyAborted: {
    [key: symbol]: boolean;
} = {};

/**
 * @module useConversationDataController
 * @description
 * 该 Hook 作为会话页面数据交互的总控制器。
 * 它整合了消息管理、SSE连接管理和会话工具（如Capsules）的功能，
 * 负责创建新的会话交互、处理用户输入、管理SSE流的生命周期，以及更新UI所需的相关状态。
 *
 * @returns {object} 返回一个包含以下方法的对象：
 * - `mockUserMsg`: (Function) 模拟用户消息发送并将其展示在UI上。
 *   接受消息内容和消息键作为参数。
 * - `cancelPreSSE`: (Function) 取消当前正在进行的SSE实例。
 *   例如，在用户发起新一轮对话或离开页面时调用。接受一个中断原因（字符串）和可选的消息键作为参数。
 * - `createConversation`: (Function) 创建一个新的会话交互流程。
 *   包括模拟用户消息、准备SSE请求参数、发起SSE连接、处理SSE消息流以及更新状态。
 *   接受一个包含消息详情、是否显示用户消息、是否显示思考中消息等选项的参数对象。
 * - `updateSessionCapsulesTools`: (Function) 更新会话中快捷短语/工具（Capsules）的数据。
 *   通常由SSE消息流中的特定事件触发。接受一个包含md5和列表数据的对象。
 */
export const useConversationDataController = () => {
    const lastReply = useRef<MsgId>('');
    const aiTextCount = useRef<number>(0);
    const sseHasSuccessed = useRef(false);
    const sseHasConnected = useRef(false);
    const lastMsgIdRef = useRef<MsgId>('');
    const sseDataHasDealed = useRef(false);
    const llmTokenHasStarted = useRef(false);
    const lastMsgExt = useRef<Record<string, unknown>>({});

    const getLastReply = () => lastReply.current;

    const sessionId = useAtomValue(curSessionIdAtom);
    const userData = useAtomValue(userDataAtom);
    const isUserInterrupted = useAtomValue(isUserInterruptedAtom);
    const updateLastMsgId = useSetAtom(lastMsgIdAtom);
    const [, setLastConversionMsgId] = useAtom(lastConversionMsgIdAtom);
    const {scrollToBottom} = useScrollControl();
    const {handleLoginBizAction} = useHandleUserLoginBizAction();
    const {status: isSwanMsgListScene} = useGetSwanMsgListSceneStatus();
    const {inputData} = useGetInputData();
    const {expertId, expert_id} = useGetUrlParams();

    const {msgIds} = useGetSessionMsgIds();
    const {updateSessionCapsulesTools: managerUpdateSessionCapsulesTools} = useSessionUtils();
    const {data: lastMsgData} = useMsgDataGetController({msgId: msgIds?.[msgIds.length - 1] || ''});

    const addSSEInstance = useCallback(
        (instance: SSEProcessorInstance<SSEResponseType>, thinkingMsgKey: MsgId) => {
            curSSEInstance.push({
                id: instance.id,
                instance,
                relatedMsgIds: [],
                thinkingMsgKey
            });
        },
        []
    );

    const addRelatedMsgIdToInstance = useCallback((sseId: symbol, msgId: MsgId) => {
        const instance = curSSEInstance.find(i => i.id === sseId);
        if (instance && !instance.relatedMsgIds.includes(msgId)) {
            instance.relatedMsgIds.push(msgId);
        }
    }, []);

    const updateRelatedMsgData = useCallback(
        (sseId: symbol) => {
            if (!sessionId) return;
            const instance = curSSEInstance.find(i => i.id === sseId);
            instance?.relatedMsgIds.forEach(msgId => {
                endTriageStreamMsgAtom(`${sessionId}_${msgId}`);
            });
        },
        [sessionId]
    );

    const removeSSEInstance = useCallback((sseId: symbol) => {
        const index = curSSEInstance.findIndex(item => item.id === sseId);
        if (index !== -1) {
            curSSEInstance.splice(index, 1);
        }
        delete isActivelyAborted[sseId];
    }, []);

    const setActivelyAborted = useCallback(
        (args: {id: symbol; status: 'aborted'; _debugSymbol?: string}) => {
            isActivelyAborted = {
                ...isActivelyAborted,
                [args.id]: args.status === 'aborted'
            };
        },
        []
    );

    // === 消息处理功能 ===
    const convertMsgToSSE = useCallback(
        async (
            msg: CreateConversationArgs['msg']
        ): Promise<{content: string | InputImgMap; contentType?: number}> => {
            const {type} = msg;
            if (type === 'image') {
                const picData = await uploadFileToBos(
                    (msg as {preData?: IPicProps[]}).preData || [],
                    {
                        count: 1,
                        bucketConfName
                    }
                );
                return {
                    content: picData[0].fileName || '',
                    ...(msg.contentType ? {contentType: msg.contentType} : {contentType: 2})
                };
            }

            if (type === 'richText') {
                const {text = '', images: imgList, ...extParams} = msg.content as RichTextContent;
                const images: InputImgMap['images'] = [] as unknown as InputImgMap['images'];

                imgList?.forEach(item => {
                    if (item.fileName) {
                        images?.push({
                            value: item.fileName,
                            path: item.path
                        });
                    }
                });
                return {
                    content: {
                        ...(extParams || {}),
                        text,
                        images
                    },
                    ...(msg.contentType ? {contentType: msg.contentType} : {contentType: 7})
                };
            }

            return {
                content: msg.content || '',
                ...(msg.contentType ? {contentType: msg.contentType} : {contentType: 1})
            };
        },
        []
    );

    const mockUserMsg = useCallback(
        (msg: CreateConversationArgs['msg'], msgKey: string): MsgItemType<unknown> | undefined => {
            const ext = {
                sessionId,
                msgKey
            };

            let msgDataProtocol: MsgItemType<unknown> | undefined;
            const msgCardIdMap: {
                [k in (typeof msg)['type']]: MSG_CARDID_TYPE;
            } = {
                text: MSG_CARDID_ENUM.ImText,
                image: MSG_CARDID_ENUM.ImImage,
                richText: MSG_CARDID_ENUM.ImRichText
            };
            if (!sessionId) return undefined;

            if (msgCardIdMap[msg.type]) {
                msgDataProtocol = convertStreamMsgProtocol({
                    ext: {
                        sessionId: ext?.sessionId || '',
                        msgKey: ext?.msgKey
                    },
                    cardId: msgCardIdMap[msg.type],
                    content: {
                        value: msg.content!,
                        ...(msg.origin ? {origin: msg.origin} : {})
                    }
                });
            }

            if (msgDataProtocol) {
                updateCurSessionMsgIdsAtom([msgKey], {
                    type: 'push'
                });
                createTriageStreamMsgAtom(`${sessionId}_${msgKey}`, msgDataProtocol);
                updateLastMsgId(msgKey);
                updateDataForUbcAtom({
                    lastMsgId: msgKey
                });
                lastMsgIdRef.current = msgKey;
            }

            return msgDataProtocol;
        },
        [sessionId, updateLastMsgId]
    );

    const mockAgreementSysMsg = useCallback(
        ({msgKey, agreementInfo}: {msgKey: MsgId; agreementInfo: AgreementDataType}) => {
            try {
                if (!sessionId) return;

                const sysMsgKey = `mock_sys_${msgKey}`;
                const sysMsgData = convertSystemMsg({
                    content: agreementInfo,
                    sessionId,
                    msgKey
                });

                updateCurSessionMsgIdsAtom([sysMsgKey], {
                    type: 'push'
                });

                createTriageStreamMsgAtom(`${sessionId}_${sysMsgKey}`, {
                    ...sysMsgData,
                    meta: {
                        ...sysMsgData.meta,
                        localExt: {
                            dataSource: 'mock',
                            insertType: 'push',
                            needScrollToBottom: true
                        }
                    }
                });

                resetAgreementInfoAtom();
            } catch (err) {
                console.error('mockAgreementSysMsg 失败：', err);
            }
        },
        [sessionId]
    );

    const addThinkMsg = useCallback(
        (msgKey: MsgId): string => {
            if (!sessionId) return '';

            const ext = {
                sessionId,
                msgKey
            };

            const data = convertStreamMsgProtocol({
                ext: {
                    sessionId: ext?.sessionId || '',
                    msgKey: ext?.msgKey
                },
                cardId: MSG_CARDID_ENUM_STREAM.ImThinking,
                content: {value: inputData?.loadingTips || ''}
            });

            if (data) {
                updateCurSessionMsgIdsAtom([msgKey], {
                    type: 'push'
                });

                createTriageStreamMsgAtom(`${sessionId}_${msgKey}`, {
                    ...data,
                    meta: {
                        ...data.meta,
                        localExt: {
                            dataSource: 'mock',
                            insertType: 'push',
                            needScrollToBottom: true
                        }
                    }
                });
                updateLastMsgId(msgKey);
                updateDataForUbcAtom({
                    lastMsgId: msgKey
                });

                lastMsgIdRef.current = msgKey;
            }

            return msgKey;
        },
        [inputData?.loadingTips, sessionId, updateLastMsgId]
    );

    // 更新消息数据
    const updateMsgData = useCallback(
        (msgItemList: MsgItemType<unknown>[], sseInstanceId: symbol, thinkingMsgMsgId: MsgId) => {
            if (!sessionId || !msgItemList || msgItemList.length === 0) return;

            setIsGeneratingAtom(true);

            msgItemList.forEach(msg => {
                const {meta, data} = msg;

                lastReply.current = meta?.msgId;

                msg.meta.localExt = {
                    dataSource: 'conversation',
                    insertType: 'push',
                    needScrollToBottom: true
                };

                const idMap = getMsgIdToKeyMappingAtomVal();

                if (idMap[meta.msgId]) {
                    updateTriageStreamMsgAtom(`${sessionId}_${idMap[meta.msgId]}`, msg, {
                        _debugSymbol: 'updateMsgData'
                    });
                } else {
                    updateCurSessionMsgIdsAtom([meta.msgId], {
                        type: 'insertBefore',
                        targetId: thinkingMsgMsgId
                    });
                    updateTriageStreamMsgAtom(`${sessionId}_${meta.msgId}`, msg, {
                        _debugSymbol: 'updateMsgData'
                    });

                    updateSpecialCardAdjectiveMsgId(
                        data?.content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE,
                        meta.msgId
                    );
                    addRelatedMsgIdToInstance(sseInstanceId, meta.msgId);

                    // 统计打断时回复字数
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const dataContent = data?.content?.data?.content as any;
                    if (dataContent && dataContent.list && Array.isArray(dataContent.list)) {
                        dataContent.list.forEach(item => {
                            if (item && typeof item.content === 'string') {
                                aiTextCount.current += item.content.length;
                            }
                        });
                    }
                    lastMsgExt.current = data?.content?.data?.ext || {};
                }
            });

            const lastMeta = msgItemList[msgItemList.length - 1].meta;
            updateLastMsgId(lastMeta.msgId);
            setLastConversionMsgId(lastMeta.msgId);
            lastMsgIdRef.current = lastMeta.msgId;
            updateDataForUbcAtom({lastMsgId: lastMeta.msgId, rounds: lastMeta.rounds || 0});
        },
        [sessionId, updateLastMsgId, setLastConversionMsgId, addRelatedMsgIdToInstance]
    );

    const onSSEComplete = useCallback(
        (arg: {
            thinkingMsgMsgId: MsgId;
            withOutThinkingMsg: boolean;
            sseId: SSEProcessorInstance<SSEResponseType>['id'];
        }) => {
            try {
                const {sseId, thinkingMsgMsgId, withOutThinkingMsg} = arg;

                if (!withOutThinkingMsg) {
                    updateCurSessionMsgIdsAtom([thinkingMsgMsgId], {
                        type: 'delete'
                    });
                }
                updateRelatedMsgData(sseId);
                removeSSEInstance(sseId);
            } catch (err) {
                console.error('SSE onComplete error:', err);
            } finally {
                setIsGeneratingAtom(false);
            }
        },
        [updateRelatedMsgData, removeSSEInstance]
    );

    const onSSEError = useCallback(
        (arg: {
            msgContent?: MsgItemType<unknown>;
            msgKey: string;
            resendArg: CreateConversationArgs;
            sseId: SSEProcessorInstance<SSEResponseType>['id'];
            errorContent: {status?: string} | unknown;
            lastReplyId: MsgId;
            hasReturnedData?: boolean;
        }) => {
            const {msgContent, msgKey, sseId, resendArg, lastReplyId} = arg;
            if (!sessionId) return;

            const isAborted =
                process.env.TARO_ENV === 'h5'
                    ? (arg.errorContent as {status?: string})?.status === 'aborted'
                    : isActivelyAborted[sseId];

            if (!isAborted) {
                createResendMsgArgAtom(msgKey, resendArg);
            }

            const lastReplyMsgData = getTriageStreamMsgDataByKey(`${sessionId}_${lastReplyId}`);

            if (lastReplyId !== '' && lastReplyMsgData) {
                const updatedMsg = {
                    ...lastReplyMsgData,
                    meta: {
                        ...lastReplyMsgData?.meta,
                        localMsgStatus: 'aborted' as const
                    }
                };

                updateTriageStreamMsgAtom(`${sessionId}_${lastReplyId}`, updatedMsg, {
                    actionType: 'update'
                });
            }

            if (msgContent) {
                const updatedMsg = {
                    ...msgContent,
                    meta: {
                        ...msgContent?.meta,
                        localMsgStatus:
                            lastReplyId !== '' && lastReplyMsgData
                                ? ('aborted' as const)
                                : ('rejected' as const)
                    }
                };
                updateTriageStreamMsgAtom(`${sessionId}_${msgKey}`, updatedMsg, {
                    _debugSymbol: 'onSSEError'
                });
            }

            removeSSEInstance(sseId);
        },
        [sessionId, removeSSEInstance]
    );

    const onSEEConnect = useCallback(
        (arg: {msgContent: MsgItemType<unknown> | undefined; msgKey: string}) => {
            const {msgContent, msgKey} = arg;
            if (!msgContent || !sessionId) return;

            const updatedMsg: MsgItemType<unknown> = {
                ...msgContent,
                meta: {
                    ...msgContent?.meta,
                    localMsgStatus: 'success'
                }
            };
            updateTriageStreamMsgAtom(`${sessionId}_${msgKey}`, updatedMsg, {
                _debugSymbol: 'onSEEConnect'
            });
        },
        [sessionId]
    );

    // 处理SSE控制数据
    const processCtrlData = useCallback(
        (
            ctrlData: NonNullable<SSEResponseType['data']>['ctrlData'],
            thinkingMsgMsgId: MsgId,
            startSSETime: number
        ) => {
            if (ctrlData?.userMsgACK) {
                const {userMsgID, userMsgKey} = ctrlData.userMsgACK;
                if (userMsgKey && userMsgID) {
                    setMsgIdToKeyMapping(userMsgID, userMsgKey);
                }
            }

            if (ctrlData?.toast) {
                showToast({
                    title: ctrlData.toast,
                    icon: 'none',
                    duration: 3000
                });
            }

            if (ctrlData?.riskControl) {
                updateMsgRiskControlAtom(ctrlData.riskControl);
            }

            if (!llmTokenHasStarted.current && ctrlData?.convStatus === 'start') {
                llmTokenHasStarted.current = true;
                const firstResponseTime = new Date().getTime();

                if (startSSETime) {
                    const tokenTime = firstResponseTime - startSSETime;
                    ubcCommonViewSend({
                        value: 'tokenFirstTimeApi',
                        ext: {
                            product_info: {
                                costTime: tokenTime,
                                msgId: '',
                                talkId: '',
                                ...(lastMsgExt.current ?? {})
                            }
                        }
                    });
                }
                updateCurSessionMsgIdsAtom([thinkingMsgMsgId], {type: 'delete'});
            }
        },
        []
    );

    // 处理用户消息创建逻辑
    const handleUserMsgCreation = useCallback(
        (arg: CreateConversationArgs, msgKey: string) => {
            const agreementInfo = getAgreementInfoAtom();
            const mockUserMsgContent = mockUserMsg(arg.msg, msgKey);

            if (!arg.ctrlData?.firstCall && agreementInfo) {
                mockAgreementSysMsg({msgKey, agreementInfo});
            }

            scrollToBottom('user_send_msg_to_wall');

            ubcCommonViewSend({
                value: 'tokenAllCountApi',
                ext: {
                    product_info: {
                        msgId: lastMsgIdRef.current,
                        ...(lastMsgExt.current ?? {})
                    }
                }
            });

            return mockUserMsgContent;
        },
        [mockUserMsg, mockAgreementSysMsg, scrollToBottom]
    );

    // 取消前置 SSE 连接
    const cancelPreSSE = useCallback(
        (reason: string, msgKeyForStatusUpdate?: string) => {
            if (!sessionId) return;

            const preSEEInstance = curSSEInstance.pop();
            if (preSEEInstance?.instance) {
                preSEEInstance?.instance?.close?.();

                if (process.env.TARO_ENV !== 'h5') {
                    setActivelyAborted({
                        id: preSEEInstance.id,
                        status: 'aborted'
                    });
                }

                updateCurSessionMsgIdsAtom([preSEEInstance?.thinkingMsgKey], {
                    type: 'delete'
                });

                preSEEInstance.relatedMsgIds.forEach(msgId => {
                    endTriageStreamMsgAtom(`${sessionId}_${msgId}`);
                });

                if (msgKeyForStatusUpdate) {
                    const msgData = getTriageStreamMsgDataByKey(
                        `${sessionId}_${msgKeyForStatusUpdate}`
                    );
                    if (msgData) {
                        const updatedMsg = {
                            ...msgData,
                            meta: {
                                ...msgData.meta,
                                localMsgStatus: 'success' as const
                            }
                        };
                        updateTriageStreamMsgAtom(
                            `${sessionId}_${msgKeyForStatusUpdate}`,
                            updatedMsg
                        );
                    }
                }

                const eventId = preSEEInstance?.instance?.getCurrentEventId();

                getUseractionReq<'stopConv'>({
                    bizActionType: 'stopConv',
                    chatData: {
                        sessionId,
                        expertId: Number(expertId || expert_id)
                    },
                    bizActionData: {
                        stopConvInfo: {
                            reason,
                            eventId: String(eventId),
                            msgId: lastMsgIdRef.current
                        }
                    }
                });
            }
        },
        [sessionId, setActivelyAborted, expertId, expert_id]
    );

    // 创建会话 - 核心功能
    const createConversation = useCallback(
        async (arg: CreateConversationArgs) => {
            if (!sessionId) return;

            try {
                // === 阶段1：参数准备 ===
                const {
                    msg,
                    withOutMsg = false,
                    withOutThinkingMsg = false,
                    ctrlData,
                    formSubmitData,
                    intent,
                    sseOnErrorCallback
                } = arg;

                const passthroughData = lastMsgData?.meta.passthroughData || arg.passthroughData;
                const msgKey = genImMsgKey(10);
                const thinkingMsgMsgId = `${MSG_THINKING_MSG_KEY_PREFIX}${msgKey}`;

                cancelPreSSE('用户新建会话主动停止', msgKey);

                // === 阶段2：创建用户消息 ===
                const mockUserMsgContent = withOutMsg
                    ? undefined
                    : handleUserMsgCreation(arg, msgKey);

                // === 阶段3：重置状态 ===
                sseHasSuccessed.current = false;
                sseDataHasDealed.current = false;
                llmTokenHasStarted.current = false;
                sseHasConnected.current = false;
                aiTextCount.current = 0;
                lastMsgExt.current = {};

                const startSSETime = new Date().getTime();

                ubcCommonViewSend({
                    value: 'sendTriageStreamMsg',
                    ext: {
                        product_info: {
                            msgKey: msgKey,
                            msgContent: msg?.content ? msg.content : 'unknow',
                            msgIds: msgIds?.length ? msgIds.join(',') : 'unknow'
                        }
                    }
                });

                // === 阶段4：准备SSE参数并执行 ===
                const ssePayloadMsg = await convertMsgToSSE(msg);
                const params = {
                    chatData: {
                        sessionId,
                        sceneType: msg.sceneType || 'unknown'
                    },
                    msg: {payload: [{msgKey, ...ssePayloadMsg}]},
                    ...(ctrlData?.firstCall
                        ? {ctrlData}
                        : {ctrlData: {tmpBotVersion2: 1 as const}}),
                    ...(formSubmitData?.msgId ? {formSubmitData} : {}),
                    ...(intent ? {intent} : {}),
                    ...(passthroughData ? {passthroughData} : {})
                };

                // === 阶段5：执行SSE连接 ===
                let sseInstance: SSEProcessorInstance<SSEResponseType>;

                const sseTask = conversationSSE({
                    params,
                    ops: {
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        onComplete: (res: any) => {
                            setIsGeneratingAtom(false);
                            const isSuccess =
                                (process.env.TARO_ENV === 'swan' &&
                                    (res?.status === 200 ||
                                        res?.statusCode === 200 ||
                                        res?.status === 'success')) ||
                                (process.env.TARO_ENV === 'h5' && res?.status === 'success') ||
                                (process.env.TARO_ENV === 'weapp' && res?.status === 'success');

                            if (isSuccess) {
                                sseHasSuccessed.current = true;
                                if (sseDataHasDealed.current) {
                                    onSSEComplete({
                                        sseId: sseInstance.id,
                                        thinkingMsgMsgId,
                                        withOutThinkingMsg
                                    });
                                }

                                if (!withOutMsg) {
                                    const responseTime = new Date().getTime();
                                    const tokenTime = responseTime - startSSETime;
                                    ubcCommonViewSend({
                                        value: 'tokenEndTimeApi',
                                        ext: {
                                            product_info: {
                                                costTime: tokenTime,
                                                msgId: getLastReply(),
                                                ...(lastMsgExt.current ?? {})
                                            }
                                        }
                                    });
                                }
                            } else {
                                onSSEError({
                                    msgKey,
                                    sseId: sseInstance.id,
                                    resendArg: arg,
                                    msgContent: mockUserMsgContent,
                                    errorContent: new Error(
                                        `接口请求失败，statusCode：${res?.status}`
                                    ),
                                    lastReplyId: getLastReply(),
                                    hasReturnedData: llmTokenHasStarted.current
                                });
                                console.error('conversationSSE onComplete 出错：', res);
                                updateCurSessionMsgIdsAtom([thinkingMsgMsgId], {type: 'delete'});

                                sseOnErrorCallback?.(
                                    new Error(`接口请求失败，statusCode：${res?.status}`)
                                );

                                if (isSwanMsgListScene) {
                                    const [msgParams] = params?.msg?.payload || [];
                                    if (msgParams?.content && msgParams?.contentType) {
                                        const text = transformMsg({
                                            msg:
                                                typeof msgParams.content === 'string'
                                                    ? msgParams?.content
                                                    : msgParams?.content?.text,
                                            type: msgParams?.contentType
                                        });
                                        sendSwanMsg(`${failedMsgPrefix}${text}`);
                                    }
                                }
                            }
                        },
                        onHeadersReceived: () => {
                            setIsGeneratingAtom(true);
                            onSEEConnect({msgKey, msgContent: mockUserMsgContent});
                            if (!withOutThinkingMsg) {
                                addThinkMsg(thinkingMsgMsgId);
                            }
                            llmTokenHasStarted.current = false;
                        },
                        onError: error => {
                            setIsGeneratingAtom(false);

                            if (sseHasConnected.current || isUserInterrupted) {
                                onSSEError({
                                    msgKey,
                                    sseId: sseInstance.id,
                                    resendArg: arg,
                                    msgContent: mockUserMsgContent,
                                    errorContent: error,
                                    lastReplyId: getLastReply(),
                                    hasReturnedData: llmTokenHasStarted.current
                                });
                            }

                            console.error('conversationSSE onError 出错：', error);
                            updateCurSessionMsgIdsAtom([thinkingMsgMsgId], {type: 'delete'});

                            sseOnErrorCallback?.(error);

                            ubcCommonViewSend({
                                value: 'tokenInterruptChatApi',
                                ext: {
                                    product_info: {
                                        costTextNum: aiTextCount.current || 0,
                                        msgId: getLastReply(),
                                        message: error?.message || '',
                                        ...(lastMsgExt.current ?? {})
                                    }
                                }
                            });
                            aiTextCount.current = 0;
                        }
                    }
                });

                // 请求队列，用于处理手百消息中心场景；@wanghaoyu08
                const promiseTasks: Promise<SSEProcessorInstance<SSEResponseType> | unknown>[] = [
                    sseTask
                ];

                if (!params.ctrlData.firstCall && isSwanMsgListScene) {
                    const [dispatchMsg] = params?.msg?.payload || [];
                    if (dispatchMsg.content) {
                        promiseTasks.push(
                            dispatchSwanimAction({
                                msgData: dispatchMsg as unknown as CreateConversationArgs['msg']
                            })
                        );
                    }
                }
                const [sseTaskVal, ..._restTasksResults] = await allSettled(promiseTasks);
                updateMsgRiskControlAtom(undefined);

                if (sseTaskVal.status === 'fulfilled') {
                    sseInstance = sseTaskVal.value as SSEProcessorInstance<SSEResponseType>;
                    addSSEInstance(sseInstance, thinkingMsgMsgId);

                    for await (const chunk of sseInstance.message()) {
                        if (chunk?.status !== 0 || !chunk?.data) continue;

                        sseHasConnected.current = true;

                        if (chunk.data.toolData?.capsules) {
                            managerUpdateSessionCapsulesTools(chunk.data.toolData.capsules);
                        }

                        if (chunk.data.ctrlData) {
                            processCtrlData(chunk.data.ctrlData, thinkingMsgMsgId, startSSETime);
                        }

                        if (chunk.data.message) {
                            updateMsgData(chunk.data.message, sseInstance.id, thinkingMsgMsgId);
                        }

                        if (!chunk.data.userData?.isLogin) continue;
                        if (!userData?.isLogin) {
                            handleLoginBizAction();
                        }
                    }

                    sseDataHasDealed.current = true;
                    if (sseHasSuccessed.current) {
                        onSSEComplete({
                            sseId: sseInstance.id,
                            thinkingMsgMsgId,
                            withOutThinkingMsg
                        });
                    }
                }

                // 处理并行任务结果
                if (_restTasksResults.length) {
                    _restTasksResults.forEach(item => {
                        const taskResult = item.status === 'fulfilled' ? item.value : item.reason;

                        if (
                            taskResult &&
                            typeof taskResult === 'object' &&
                            'taskType' in taskResult &&
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            (taskResult as any).taskType === 'swanMsgCenter'
                        ) {
                            syncSwanMsgStatus({
                                sseRes: sseTaskVal,
                                swanImRes: item
                            });
                        }
                    });
                }
            } catch (err: unknown) {
                setIsGeneratingAtom(false);
                ubcCommonViewSend({
                    value: 'createConversationError',
                    ext: {
                        product_info: {
                            message: err
                        }
                    }
                });
                if (err instanceof Error && err.name !== 'AbortError') {
                    console.error('Error in createConversation:', err);
                }
            }
        },
        [
            msgIds,
            sessionId,
            lastMsgData?.meta.passthroughData,
            isSwanMsgListScene,
            cancelPreSSE,
            addThinkMsg,
            convertMsgToSSE,
            addSSEInstance,
            updateMsgData,
            onSSEComplete,
            onSSEError,
            onSEEConnect,
            managerUpdateSessionCapsulesTools,
            handleLoginBizAction,
            processCtrlData,
            handleUserMsgCreation
        ]
    );

    // 暴露的模拟用户消息函数
    const exposedMockUserMsg = useCallback(
        (msg: CreateConversationArgs['msg'], msgKey: string) => {
            return mockUserMsg(msg, msgKey);
        },
        [mockUserMsg]
    );

    return {
        cancelPreSSE,
        createConversation,
        mockUserMsg: exposedMockUserMsg,
        updateSessionCapsulesTools: managerUpdateSessionCapsulesTools
    };
};
