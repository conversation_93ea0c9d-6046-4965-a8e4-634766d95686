/*
 * @Author: liulei
 * @Description: 历史记录弹层组件
 * @Features:
 * 1. 展示历史对话记录列表
 * 2. 支持删除单条记录
 * 3. 支持取消删除操作
 * 4. 空状态展示
 */
import {pxTransform, showLoading, hideLoading, eventCenter} from '@tarojs/taro';
import React, {memo, FC, useCallback, useMemo, useState, useEffect} from 'react';
import cx from 'classnames';
import {View, Text, ScrollView} from '@tarojs/components';
import {Button, SafeArea, Empty} from '@baidu/wz-taro-tools-core';
import {HPopup} from '@baidu/health-ui';
import {useAtomValue} from 'jotai';
import {WiseShut, WiseDelete, WiseIncrease} from '@baidu/wz-taro-tools-icons';
import {debounce} from 'lodash-es';
import {ubcCommonClkSend, ubcCommonViewSend} from '../../utils/generalFunction/ubc';
import {useSwitchSessionhook, useCreateSessionhook} from '../../hooks/triageStream/session';
import {getTalkList} from '../../models/services/historyRecord/getTalkList';
import {useGetSessionId} from '../../hooks/triageStream/pageDataController';
import {deleteTalk} from '../../models/services/historyRecord/delTalkList';
import {titleDataAtom} from '../../store/triageStreamAtom';
import {getExperienceConfig} from '../../store/triageStreamAtom/otherData';

import type {TalkItem} from '../../models/services/historyRecord/getTalkList/index.d';
import {formatTimeText} from './utls';
import {HistoryRecordProps} from './index.d';
import styles from './index.module.less';

// 通用按钮样式类
const commonBtnClass = 'wz-plr-18 wz-fs-57 wz-flex wz-col-center';

// 分页数据size
const PAGE_SIZE = 15;

// 节流时间
const DEBOUNCETIME = 1000;

const SkeletonItem = () => (
    <View className={cx('wz-flex wz-col-center wz-ptb-51 wz-plr-51')}>
        <View className={cx(styles.historyItem, 'wz-pr-45')}>
            <View className={cx(styles.historyText, 'wz-fs-48', styles.skeletonText)} />
            <View className={cx(styles.historyTime, 'wz-fs-39 wz-mt-27', styles.skeletonTime)} />
        </View>
        <View className={cx(styles.operate, 'wz-flex')}>
            <View className={cx(styles.skeletonDelete)} />
        </View>
    </View>
);

const SkeletonList = ({list}: {list: number}) => (
    <>
        {Array(list)
            .fill(null)
            .map((_, index) => (
                <SkeletonItem key={index} />
            ))}
    </>
);

/**
 * 历史记录弹层组件
 * @param props - 组件属性
 * @param props.setOpen - 控制弹窗开关的函数
 * @param props.open - 弹窗开关状态
 */
const HistoryRecordPopup: FC<HistoryRecordProps> = (props: HistoryRecordProps) => {
    const {setOpen, open = false} = props;

    const curSessionId = useGetSessionId();
    const {switchSession} = useSwitchSessionhook();
    const {createSession} = useCreateSessionhook();

    const [activeOperateBtnId, setActiveOperateBtnId] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [hasTalkListLoaded, setHasTalkListLoaded] = useState(false);
    const [talkList, setTalkList] = useState<TalkItem[]>([]);
    const [lastSessionId, setLastSessionId] = useState<string | undefined>(undefined);
    const titleData = useAtomValue(titleDataAtom);
    const {showImCreateEntry} = titleData || {};
    const experienceConfig = getExperienceConfig();

    // 获取对话列表
    const fetchTalkList = useCallback(
        async (viewType: 'next' | 'pre', sessionId?: string, isInit = false) => {
            setIsLoading(true);
            try {
                const params = {
                    viewType,
                    size: PAGE_SIZE,
                    sessionId: sessionId || undefined
                };
                const [, res] = await getTalkList(params);
                // 消息获取到就置为false，防止列表和loading状态同时展示
                setIsLoading(false);
                setHasTalkListLoaded(true);
                const list = res?.data?.list || [];
                setHasMore(res?.data?.hasMore ?? false);
                if (isInit) {
                    setTalkList(list);
                } else {
                    setTalkList(prev => [...prev, ...list]);
                }
                if (list.length > 0) {
                    setLastSessionId(list[list.length - 1].sessionId);
                }
            } catch (error) {
                setHasMore(false);
                // 可根据需要增加错误提示
            } finally {
                setIsLoading(false);
            }
        },
        []
    );

    // 初始化接口数据
    useEffect(() => {
        if (open) {
            setTalkList([]);
            setHasMore(true);
            setHasTalkListLoaded(false);
            setLastSessionId(undefined);
            fetchTalkList('next', undefined, true);
            ubcCommonViewSend({
                value: 'ImHistoryList',
                ext: {
                    product_info: {
                        sessionId: curSessionId || ''
                    }
                }
            });
        }
    }, [curSessionId, fetchTalkList, open]);

    /**
     * 关闭弹窗
     */
    const handleClose = useCallback(() => {
        setOpen?.(false);
        setActiveOperateBtnId('');
    }, [setOpen]);

    /**
     * 处理加载更多
     */
    const handleLoadMore = useCallback(async () => {
        if (isLoading || !hasMore) return;
        fetchTalkList('next', lastSessionId);
    }, [isLoading, hasMore, lastSessionId, fetchTalkList]);

    /**
     * 删除TalkItem 数据
     */
    const handleDelTalkItem = useCallback(async () => {
        if (!activeOperateBtnId) return;
        showLoading({
            title: '正在删除...',
            mask: true
        });
        try {
            await deleteTalk({sessionId: activeOperateBtnId});
            setTalkList(prev => prev.filter(item => item.sessionId !== activeOperateBtnId));

            // 如果删除会话是当前聊天会话则新建会话
            if (activeOperateBtnId === curSessionId) {
                createSession();
            }
            setActiveOperateBtnId('');

            hideLoading();

            // 如果当前页数据不足，且不是最后一页，加载更多数据
            if (talkList.length <= PAGE_SIZE && hasMore) {
                handleLoadMore();
            }
        } catch (error) {
            console.error(error);
            // 可根据需要增加错误提示
        }
    }, [activeOperateBtnId, curSessionId, talkList.length, hasMore, createSession, handleLoadMore]);

    /**
     * 切换会话 数据
     */
    const handleSwitchSession = useCallback(
        talk => {
            const {sessionId} = talk;
            switchSession(sessionId, 'sessionList');
            handleClose();
            eventCenter.trigger('clearImgAndInput');
        },
        [handleClose, switchSession]
    );

    /**
     * 处理滚动到底部
     */
    const handleScrollToLower = useCallback(() => {
        handleLoadMore();
    }, [handleLoadMore]);

    /**
     * 点击新建对话按钮埋点
     */
    const handleUbcClk = useCallback(() => {
        ubcCommonClkSend({
            value: 'ImCreateSession',
            ext: {
                // eslint-disable-next-line camelcase
                product_info: {
                    sessionId: curSessionId || ''
                }
            }
        });
    }, [curSessionId]);

    const renderEmpty = useMemo(() => {
        const newIp = experienceConfig?.supportLike;
        const ipImg = newIp
            ? 'https://med-fe.cdn.bcebos.com/vita/defaultDiagram.png'
            : 'https://med-fe.cdn.bcebos.com/common/pageStatus/status_empty.png';

        return (
            <Empty
                className={cx(
                    styles.historyRecordEmpty,
                    newIp && styles.historyRecordEmptyNewIp,
                    'wz-flex'
                )}
            >
                <Empty.Image className={styles.emptyImg} src={ipImg} />
                <Empty.Description>暂无历史记录</Empty.Description>
            </Empty>
        );
    }, []);

    /**
     * 渲染操作区域（删除按钮）
     * @param sessionId - 对话组ID
     * @returns 操作按钮组件
     */
    const renderDelContent = useCallback(
        (sessionId: string) => {
            // 当前对话组显示取消和删除按钮
            if (sessionId === activeOperateBtnId) {
                return (
                    <View className={cx(styles.operate, 'wz-flex')}>
                        <Button
                            size='small'
                            className={cx(styles.operateBtn, commonBtnClass)}
                            style={{
                                backgroundColor: '#B5B5B5',
                                marginRight: pxTransform(24)
                            }}
                            onClick={() => {
                                setActiveOperateBtnId('');
                            }}
                        >
                            取消
                        </Button>
                        <Button
                            size='small'
                            color='danger'
                            className={cx(styles.operateBtn, commonBtnClass)}
                            onClick={debounce(() => handleDelTalkItem(), DEBOUNCETIME, {
                                leading: true,
                                trailing: false
                            })}
                        >
                            删除
                            <WiseDelete className={styles.operateBtnIcon} />
                        </Button>
                    </View>
                );
            }

            // 其他对话组只显示删除图标
            return (
                <WiseDelete
                    size={60}
                    onClick={() => {
                        setActiveOperateBtnId(sessionId);
                    }}
                />
            );
        },
        [activeOperateBtnId, handleDelTalkItem]
    );

    /**
     * 渲染单个对话项
     * @param talk - 对话数据
     * @returns 对话项组件
     */
    const renderTalkItem = useCallback(
        (talk: TalkItem) => {
            return (
                <>
                    <View
                        className={cx(styles.historyItem, 'wz-pr-45')}
                        onClick={() => {
                            handleSwitchSession(talk);
                        }}
                    >
                        <View className={cx(styles.historyText, 'wz-fs-48')}>
                            {talk.firstMsgText || ''}
                        </View>
                        <View className={cx(styles.historyTime, 'wz-fs-39 wz-mt-27')}>
                            {formatTimeText(talk.createTime)}
                        </View>
                    </View>
                    {renderDelContent(talk.sessionId || '')}
                </>
            );
        },
        [handleSwitchSession, renderDelContent]
    );

    /**
     * 渲染对话列表
     */
    const renderTalkList = useMemo(() => {
        if (talkList.length === 0 && !isLoading) {
            return <View className={cx(styles.historyRecordEmpty)}>{renderEmpty}</View>;
        }
        if (talkList.length === 0) return null;
        return talkList?.map(talk => {
            const isCurTalk = talk.sessionId === activeOperateBtnId;
            return (
                <View
                    className={cx(
                        isCurTalk ? styles.btnActive : '',
                        `wz-flex wz-col-center wz-ptb-51 wz-pl-51 ${isCurTalk ? 'wz-pr-21' : 'wz-pr-45'}`
                    )}
                    key={talk?.sessionId}
                >
                    {renderTalkItem(talk)}
                </View>
            );
        });
    }, [isLoading, talkList, renderEmpty, activeOperateBtnId, renderTalkItem]);

    /**
     * 渲染加载状态
     */
    const renderLoading = useMemo(() => {
        if (!talkList?.length) {
            return null;
        }
        const text = !hasMore ? '没有更多了' : isLoading ? '加载中...' : '';

        return (
            <View className={cx('wz-flex wz-row-center wz-col-center wz-ptb-45')}>
                <View className={cx(styles.loadingText, 'wz-fs-42 wz-text-center')}>{text}</View>
            </View>
        );
    }, [talkList?.length, hasMore, isLoading]);

    // 修复苹果sse骨架屏偶现不消失的问题
    const memoForcerenderKey = useMemo(() => {
        if (process.env.TARO_ENV === 'swan') {
            return talkList?.length ? 'list' : 'empty';
        }
        return 'default';
    }, [talkList?.length]);

    /**
     * 渲染历史记录列表主体
     */
    const memoHistoryList = useMemo(() => {
        return (
            <View className={cx(styles.historyCon, 'wz-flex')}>
                {/* 标题栏 */}
                <View
                    className={cx(
                        styles.historyTitle,
                        'wz-fs-54 wz-row-between wz-flex wz-col-center wz-plr-51'
                    )}
                >
                    <Text>历史记录</Text>
                    <WiseShut size={60} onClick={handleClose} />
                </View>
                {/* 列表区域 */}
                <ScrollView
                    scrollY
                    className={cx(styles.historyListWrap)}
                    key={memoForcerenderKey}
                    onScrollToLower={handleScrollToLower}
                >
                    {!hasTalkListLoaded && <SkeletonList list={6} />}
                    {hasTalkListLoaded && (
                        <>
                            {renderTalkList}
                            {renderLoading}
                        </>
                    )}
                </ScrollView>
                {/* 操作区 */}
                {showImCreateEntry ? (
                    <View className={styles.hideDialogBtn}>
                        <SafeArea position='bottom' />
                    </View>
                ) : (
                    <View className={cx(styles.dialogBtn, 'wz-plr-51')}>
                        <Button
                            className={cx(styles.btn)}
                            size='large'
                            onClick={debounce(
                                () => {
                                    handleUbcClk();
                                    createSession();
                                    handleClose();
                                },
                                DEBOUNCETIME,
                                {leading: true, trailing: false}
                            )}
                        >
                            <WiseIncrease size={54} className={styles.btnIcon} />
                            新建对话
                        </Button>
                        <SafeArea position='bottom' />
                    </View>
                )}
            </View>
        );
    }, [
        handleClose,
        memoForcerenderKey,
        handleScrollToLower,
        hasTalkListLoaded,
        renderTalkList,
        renderLoading,
        createSession,
        showImCreateEntry,
        handleUbcClk
    ]);

    // 渲染弹窗组件
    return (
        <HPopup
            show={open}
            onClose={handleClose}
            rounded
            openSafeAreaInsetBottom={false}
            contentStyle={{
                padding: 0
            }}
            position='left'
            width='87vw'
            height='100%'
            content={memoHistoryList}
        />
    );
};

HistoryRecordPopup.displayName = 'HistoryRecordPopup';
export default memo(HistoryRecordPopup);
